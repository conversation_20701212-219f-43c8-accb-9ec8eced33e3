/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */

// Note: In a real implementation, you would import ParseSourceSpan from '@angular/compiler'
// For this example, we'll use our own mock implementation
import {
  TmplAstNode,
  TmplAstElement,
  TmplAstText,
  TmplAstTextAttribute,
  TmplAstBoundAttribute,
  TmplAstBoundEvent,
  TmplAstReference,
  TmplAstVariable,
  TmplAstTemplate,
  TmplAstContent,
  TmplAstBoundText,
  TmplAstIcu,
  TmplAstDeferredBlock,
  TmplAstRecursiveVisitor,
  TmplAstVisitor
} from './tmpl-ast-visit-all';

// Mock ParseSourceSpan interface for this example
interface ParseSourceSpan {
  start: any;
  end: any;
  fullStart: any;
  details: any;
  toString(): string;
}
import { tmplAstVisitAll } from './tmpl-ast-visit-all';

// Mock ParseSourceSpan for testing
class MockParseSourceSpan implements ParseSourceSpan {
  constructor(
    public start: any = { offset: 0, line: 0, col: 0 },
    public end: any = { offset: 0, line: 0, col: 0 },
    public fullStart: any = { offset: 0, line: 0, col: 0 },
    public details: any = null
  ) {}

  toString(): string {
    return 'mock-span';
  }
}

// Extended interfaces to include i18n properties for this example
interface ExtendedTmplAstElement extends TmplAstElement {
  i18n?: any;
}

interface ExtendedTmplAstTextAttribute extends TmplAstTextAttribute {
  i18n?: any;
}

// Mock implementations for testing
class MockTmplAstTextAttribute implements ExtendedTmplAstTextAttribute {
  constructor(
    public name: string,
    public value: string,
    public sourceSpan: ParseSourceSpan = new MockParseSourceSpan(),
    public keySpan?: ParseSourceSpan,
    public valueSpan?: ParseSourceSpan,
    public i18n?: any
  ) {}

  visit<Result>(visitor: TmplAstVisitor<Result>): Result {
    return visitor.visitTextAttribute(this);
  }
}

class MockTmplAstElement implements ExtendedTmplAstElement {
  constructor(
    public name: string,
    public attributes: ExtendedTmplAstTextAttribute[] = [],
    public inputs: TmplAstBoundAttribute[] = [],
    public outputs: TmplAstBoundEvent[] = [],
    public children: TmplAstNode[] = [],
    public references: TmplAstReference[] = [],
    public sourceSpan: ParseSourceSpan = new MockParseSourceSpan(),
    public i18n?: any
  ) {}

  visit<Result>(visitor: TmplAstVisitor<Result>): Result {
    return visitor.visitElement(this);
  }
}

class MockTmplAstText implements TmplAstText {
  constructor(
    public value: string,
    public sourceSpan: ParseSourceSpan = new MockParseSourceSpan()
  ) {}

  visit<Result>(visitor: TmplAstVisitor<Result>): Result {
    return visitor.visitText(this);
  }
}

/**
 * Interface representing an element with i18n attributes found during template analysis
 */
interface I18nElementInfo {
  elementName: string;
  hasDirectI18n: boolean;
  directI18nValue?: string;
  i18nAttributes: Array<{
    attributeName: string;
    i18nKey: string;
    i18nValue: string;
  }>;
  sourceLocation: string;
}

/**
 * Custom visitor that extends TmplAstRecursiveVisitor to identify i18n usage in templates.
 * This visitor analyzes each element for:
 * 1. Direct i18n attributes on the element itself
 * 2. i18n-prefixed attributes for specific attribute internationalization
 * 3. Any attributes that have i18n metadata
 */
class I18nAnalysisVisitor extends TmplAstRecursiveVisitor {
  private i18nElements: I18nElementInfo[] = [];

  /**
   * Get all elements that have i18n-related attributes
   */
  getI18nElements(): I18nElementInfo[] {
    return this.i18nElements;
  }

  /**
   * Visit an element and analyze it for i18n attributes
   */
  override visitElement(element: TmplAstElement): void {
    // Cast to extended interface to access i18n property
    const extendedElement = element as ExtendedTmplAstElement;
    const i18nInfo: I18nElementInfo = {
      elementName: element.name,
      hasDirectI18n: false,
      i18nAttributes: [],
      sourceLocation: element.sourceSpan.toString()
    };

    let hasI18nContent = false;

    // Check for direct i18n attribute on the element
    if (extendedElement.i18n) {
      i18nInfo.hasDirectI18n = true;
      i18nInfo.directI18nValue = this.extractI18nValue(extendedElement.i18n);
      hasI18nContent = true;
    }

    // Check all attributes for i18n-related content
    element.attributes.forEach(attr => {
      const extendedAttr = attr as ExtendedTmplAstTextAttribute;
      if (extendedAttr.name === 'i18n') {
        // Direct i18n attribute
        i18nInfo.hasDirectI18n = true;
        i18nInfo.directI18nValue = extendedAttr.value;
        hasI18nContent = true;
      } else if (extendedAttr.name.startsWith('i18n-')) {
        // i18n attribute for specific attribute (e.g., i18n-title, i18n-alt)
        const targetAttribute = extendedAttr.name.substring(5); // Remove 'i18n-' prefix
        i18nInfo.i18nAttributes.push({
          attributeName: targetAttribute,
          i18nKey: extendedAttr.name,
          i18nValue: extendedAttr.value
        });
        hasI18nContent = true;
      } else if (extendedAttr.i18n) {
        // Attribute has i18n metadata
        i18nInfo.i18nAttributes.push({
          attributeName: extendedAttr.name,
          i18nKey: 'i18n-' + extendedAttr.name,
          i18nValue: this.extractI18nValue(extendedAttr.i18n)
        });
        hasI18nContent = true;
      }
    });

    // Only add to results if the element has i18n content
    if (hasI18nContent) {
      this.i18nElements.push(i18nInfo);
    }

    // Continue recursive traversal
    super.visitElement(element);
  }

  /**
   * Extract i18n value from i18n metadata object
   */
  private extractI18nValue(i18nMeta: any): string {
    if (typeof i18nMeta === 'string') {
      return i18nMeta;
    }
    if (i18nMeta && typeof i18nMeta === 'object') {
      return i18nMeta.description || i18nMeta.meaning || i18nMeta.id || 'i18n-enabled';
    }
    return 'i18n-enabled';
  }
}

/**
 * Create sample template AST nodes that represent a parsed HTML template
 * with various i18n scenarios
 */
function createSampleTemplateNodes(): TmplAstNode[] {
  // Create attributes for different i18n scenarios
  const i18nAttr = new MockTmplAstTextAttribute('i18n', '@@welcome');
  const titleAttr = new MockTmplAstTextAttribute('title', 'Welcome message');
  const i18nTitleAttr = new MockTmplAstTextAttribute('i18n-title', '@@welcome.title');
  
  const altAttr = new MockTmplAstTextAttribute('alt', 'Image description');
  const i18nAltAttr = new MockTmplAstTextAttribute('i18n-alt', '@@image.alt');
  
  const classAttr = new MockTmplAstTextAttribute('class', 'container');

  // Create text nodes
  const welcomeText = new MockTmplAstText('Welcome to our application!');
  const buttonText = new MockTmplAstText('Click me');
  const plainText = new MockTmplAstText('This is plain text');

  // Create elements with different i18n configurations
  
  // 1. Element with direct i18n attribute
  const welcomeDiv = new MockTmplAstElement(
    'div',
    [i18nAttr, classAttr],
    [], [], 
    [welcomeText],
    []
  );

  // 2. Element with i18n on specific attribute (title)
  const tooltipSpan = new MockTmplAstElement(
    'span',
    [titleAttr, i18nTitleAttr],
    [], [],
    [new MockTmplAstText('Hover for tooltip')],
    []
  );

  // 3. Image with i18n alt attribute
  const imageElement = new MockTmplAstElement(
    'img',
    [altAttr, i18nAltAttr, new MockTmplAstTextAttribute('src', '/logo.png')],
    [], [], 
    [],
    []
  );

  // 4. Button with both direct i18n and i18n attribute
  const buttonElement = new MockTmplAstElement(
    'button',
    [
      new MockTmplAstTextAttribute('i18n', '@@button.submit'),
      new MockTmplAstTextAttribute('title', 'Submit form'),
      new MockTmplAstTextAttribute('i18n-title', '@@button.submit.title')
    ],
    [], [],
    [buttonText],
    []
  );

  // 5. Element without any i18n attributes (for comparison)
  const plainDiv = new MockTmplAstElement(
    'div',
    [classAttr],
    [], [],
    [plainText],
    []
  );

  // 6. Container element with i18n children
  const containerDiv = new MockTmplAstElement(
    'div',
    [new MockTmplAstTextAttribute('class', 'main-container')],
    [], [],
    [welcomeDiv, tooltipSpan, imageElement, buttonElement, plainDiv],
    []
  );

  return [containerDiv];
}

/**
 * Demonstrate the i18n analysis visitor
 */
function demonstrateI18nAnalysis(): void {
  console.log('=== I18n Template Analysis Example ===\n');

  // Create sample template nodes
  const templateNodes = createSampleTemplateNodes();

  console.log('Sample HTML Template (conceptual):');
  console.log(`
<div class="main-container">
  <div i18n="@@welcome" class="container">Welcome to our application!</div>
  <span title="Welcome message" i18n-title="@@welcome.title">Hover for tooltip</span>
  <img alt="Image description" i18n-alt="@@image.alt" src="/logo.png">
  <button i18n="@@button.submit" title="Submit form" i18n-title="@@button.submit.title">Click me</button>
  <div class="container">This is plain text</div>
</div>
  `);

  // Create and run the i18n analysis visitor
  const i18nVisitor = new I18nAnalysisVisitor();
  tmplAstVisitAll(i18nVisitor, templateNodes);

  // Get results
  const i18nElements = i18nVisitor.getI18nElements();

  console.log('\n=== I18n Analysis Results ===\n');
  console.log(`Found ${i18nElements.length} elements with i18n attributes:\n`);

  i18nElements.forEach((element, index) => {
    console.log(`${index + 1}. Element: <${element.elementName}>`);
    console.log(`   Source: ${element.sourceLocation}`);
    
    if (element.hasDirectI18n) {
      console.log(`   ✓ Direct i18n: "${element.directI18nValue}"`);
    }
    
    if (element.i18nAttributes.length > 0) {
      console.log(`   ✓ I18n attributes:`);
      element.i18nAttributes.forEach(attr => {
        console.log(`     - ${attr.i18nKey}: "${attr.i18nValue}" (for attribute: ${attr.attributeName})`);
      });
    }
    
    console.log('');
  });

  // Summary statistics
  const elementsWithDirectI18n = i18nElements.filter(el => el.hasDirectI18n).length;
  const elementsWithI18nAttrs = i18nElements.filter(el => el.i18nAttributes.length > 0).length;
  const totalI18nAttributes = i18nElements.reduce((sum, el) => sum + el.i18nAttributes.length, 0);

  console.log('=== Summary ===');
  console.log(`Elements with direct i18n: ${elementsWithDirectI18n}`);
  console.log(`Elements with i18n attributes: ${elementsWithI18nAttrs}`);
  console.log(`Total i18n attributes found: ${totalI18nAttributes}`);
}

/**
 * Advanced example showing how to integrate with Angular's actual template parsing
 * Note: This would require importing from @angular/compiler in a real implementation
 */
function demonstrateRealTemplateParsingIntegration(): void {
  console.log('\n=== Integration with Real Template Parsing ===\n');

  const htmlTemplate = `
    <div class="welcome-section">
      <h1 i18n="@@page.title">Welcome to Angular</h1>
      <p i18n="@@page.description">This is an internationalized application.</p>
      <img src="/assets/logo.png"
           alt="Angular logo"
           i18n-alt="@@logo.alt">
      <button type="submit"
              title="Submit the form"
              i18n-title="@@form.submit.tooltip"
              i18n="@@form.submit.button">
        Submit
      </button>
      <div class="footer">
        <span>Copyright 2024</span>
      </div>
    </div>
  `;

  console.log('HTML Template to analyze:');
  console.log(htmlTemplate);

  console.log('\nTo parse this with Angular compiler, you would use:');
  console.log(`
// Import from @angular/compiler
import { parseTemplate } from '@angular/compiler';

// Parse the template
const parsed = parseTemplate(htmlTemplate, 'template.html', {
  preserveWhitespaces: false,
  enableI18nLegacyMessageIdFormat: false
});

// Use our visitor on the parsed nodes
const i18nVisitor = new I18nAnalysisVisitor();
tmplAstVisitAll(i18nVisitor, parsed.nodes);

// Get i18n analysis results
const results = i18nVisitor.getI18nElements();
  `);
}

/**
 * Example showing how to create a visitor that generates i18n extraction reports
 */
class I18nExtractionReportVisitor extends TmplAstRecursiveVisitor {
  private extractionReport: {
    totalElements: number;
    i18nElements: number;
    missingI18nElements: string[];
    i18nMessages: Array<{
      id: string;
      description?: string;
      meaning?: string;
      sourceFile: string;
      elementType: string;
    }>;
  } = {
    totalElements: 0,
    i18nElements: 0,
    missingI18nElements: [],
    i18nMessages: []
  };

  getExtractionReport() {
    return this.extractionReport;
  }

  override visitElement(element: TmplAstElement): void {
    const extendedElement = element as ExtendedTmplAstElement;
    this.extractionReport.totalElements++;

    const hasTextContent = element.children.some(child =>
      child instanceof MockTmplAstText && child.value.trim().length > 0
    );

    let hasI18n = false;

    // Check for direct i18n
    if (extendedElement.i18n) {
      hasI18n = true;
      this.extractionReport.i18nMessages.push({
        id: this.extractI18nId(extendedElement.i18n),
        description: this.extractI18nDescription(extendedElement.i18n),
        meaning: this.extractI18nMeaning(extendedElement.i18n),
        sourceFile: 'template.html',
        elementType: element.name
      });
    }

    // Check attributes for i18n
    element.attributes.forEach(attr => {
      const extendedAttr = attr as ExtendedTmplAstTextAttribute;
      if (extendedAttr.name === 'i18n') {
        hasI18n = true;
        this.extractionReport.i18nMessages.push({
          id: this.parseI18nValue(extendedAttr.value).id || 'auto-generated',
          description: this.parseI18nValue(extendedAttr.value).description,
          meaning: this.parseI18nValue(extendedAttr.value).meaning,
          sourceFile: 'template.html',
          elementType: element.name
        });
      } else if (extendedAttr.name.startsWith('i18n-')) {
        hasI18n = true;
        const targetAttr = extendedAttr.name.substring(5);
        this.extractionReport.i18nMessages.push({
          id: this.parseI18nValue(extendedAttr.value).id || `auto-${element.name}-${targetAttr}`,
          description: this.parseI18nValue(extendedAttr.value).description,
          meaning: this.parseI18nValue(extendedAttr.value).meaning,
          sourceFile: 'template.html',
          elementType: `${element.name}[${targetAttr}]`
        });
      }
    });

    if (hasI18n) {
      this.extractionReport.i18nElements++;
    } else if (hasTextContent) {
      this.extractionReport.missingI18nElements.push(
        `<${element.name}> with text content but no i18n attribute`
      );
    }

    super.visitElement(element);
  }

  private extractI18nId(i18nMeta: any): string {
    if (typeof i18nMeta === 'string') {
      return this.parseI18nValue(i18nMeta).id || 'auto-generated';
    }
    return i18nMeta?.id || 'auto-generated';
  }

  private extractI18nDescription(i18nMeta: any): string | undefined {
    if (typeof i18nMeta === 'string') {
      return this.parseI18nValue(i18nMeta).description;
    }
    return i18nMeta?.description;
  }

  private extractI18nMeaning(i18nMeta: any): string | undefined {
    if (typeof i18nMeta === 'string') {
      return this.parseI18nValue(i18nMeta).meaning;
    }
    return i18nMeta?.meaning;
  }

  private parseI18nValue(value: string): { id?: string; description?: string; meaning?: string } {
    // Parse i18n attribute value like "meaning|description@@id"
    const result: { id?: string; description?: string; meaning?: string } = {};

    if (value.includes('@@')) {
      const [meaningDesc, id] = value.split('@@');
      result.id = id;

      if (meaningDesc.includes('|')) {
        const [meaning, description] = meaningDesc.split('|');
        result.meaning = meaning || undefined;
        result.description = description || undefined;
      } else {
        result.description = meaningDesc || undefined;
      }
    } else {
      result.description = value || undefined;
    }

    return result;
  }
}

/**
 * Example of a more specialized visitor that focuses on specific i18n patterns
 */
class I18nValidationVisitor extends TmplAstRecursiveVisitor {
  private issues: Array<{
    element: string;
    issue: string;
    severity: 'warning' | 'error';
    location: string;
  }> = [];

  getValidationIssues() {
    return this.issues;
  }

  override visitElement(element: TmplAstElement): void {
    // Check for common i18n issues
    this.validateI18nUsage(element);
    super.visitElement(element);
  }

  private validateI18nUsage(element: TmplAstElement): void {
    const extendedElement = element as ExtendedTmplAstElement;
    const hasI18nAttr = element.attributes.some(attr => attr.name === 'i18n');
    const hasI18nSpecificAttrs = element.attributes.some(attr => attr.name.startsWith('i18n-'));
    const hasTextContent = element.children.some(child =>
      child instanceof MockTmplAstText && child.value.trim().length > 0
    );

    // Warning: Text content without i18n
    if (hasTextContent && !hasI18nAttr && !extendedElement.i18n) {
      this.issues.push({
        element: element.name,
        issue: 'Element contains text content but no i18n attribute',
        severity: 'warning',
        location: element.sourceSpan.toString()
      });
    }

    // Error: i18n attribute without proper ID
    element.attributes.forEach(attr => {
      if (attr.name === 'i18n' && !attr.value.includes('@@')) {
        this.issues.push({
          element: element.name,
          issue: `i18n attribute "${attr.value}" should include a unique ID with @@`,
          severity: 'error',
          location: element.sourceSpan.toString()
        });
      }
    });

    // Warning: Missing i18n for common attributes
    const commonI18nAttributes = ['title', 'alt', 'placeholder', 'aria-label'];
    element.attributes.forEach(attr => {
      if (commonI18nAttributes.includes(attr.name)) {
        const hasCorrespondingI18n = element.attributes.some(
          i18nAttr => i18nAttr.name === `i18n-${attr.name}`
        );
        if (!hasCorrespondingI18n) {
          this.issues.push({
            element: element.name,
            issue: `Attribute "${attr.name}" might need internationalization (i18n-${attr.name})`,
            severity: 'warning',
            location: element.sourceSpan.toString()
          });
        }
      }
    });
  }
}

/**
 * Demonstrate i18n validation
 */
function demonstrateI18nValidation(): void {
  console.log('\n=== I18n Validation Example ===\n');

  // Create nodes with various i18n issues for demonstration
  const problematicNodes = [
    // Text without i18n
    new MockTmplAstElement(
      'div',
      [],
      [], [],
      [new MockTmplAstText('This text needs i18n!')],
      []
    ),
    // i18n without proper ID
    new MockTmplAstElement(
      'span',
      [new MockTmplAstTextAttribute('i18n', 'some description')],
      [], [],
      [new MockTmplAstText('Hello')],
      []
    ),
    // Missing i18n for title attribute
    new MockTmplAstElement(
      'button',
      [new MockTmplAstTextAttribute('title', 'Click me')],
      [], [],
      [new MockTmplAstText('Submit')],
      []
    )
  ];

  const validationVisitor = new I18nValidationVisitor();
  tmplAstVisitAll(validationVisitor, problematicNodes);

  const issues = validationVisitor.getValidationIssues();

  console.log(`Found ${issues.length} i18n validation issues:\n`);

  issues.forEach((issue, index) => {
    const icon = issue.severity === 'error' ? '❌' : '⚠️';
    console.log(`${index + 1}. ${icon} ${issue.severity.toUpperCase()}: ${issue.issue}`);
    console.log(`   Element: <${issue.element}>`);
    console.log(`   Location: ${issue.location}\n`);
  });
}

/**
 * Demonstrate i18n extraction report generation
 */
function demonstrateI18nExtractionReport(): void {
  console.log('\n=== I18n Extraction Report Example ===\n');

  // Create sample nodes for extraction report
  const templateNodes = createSampleTemplateNodes();

  const extractionVisitor = new I18nExtractionReportVisitor();
  tmplAstVisitAll(extractionVisitor, templateNodes);

  const report = extractionVisitor.getExtractionReport();

  console.log('I18n Extraction Report:');
  console.log('======================');
  console.log(`Total elements analyzed: ${report.totalElements}`);
  console.log(`Elements with i18n: ${report.i18nElements}`);
  console.log(`Coverage: ${((report.i18nElements / report.totalElements) * 100).toFixed(1)}%\n`);

  if (report.i18nMessages.length > 0) {
    console.log('Extracted i18n messages:');
    report.i18nMessages.forEach((msg, index) => {
      console.log(`${index + 1}. ID: ${msg.id}`);
      console.log(`   Element: ${msg.elementType}`);
      if (msg.description) console.log(`   Description: ${msg.description}`);
      if (msg.meaning) console.log(`   Meaning: ${msg.meaning}`);
      console.log(`   Source: ${msg.sourceFile}\n`);
    });
  }

  if (report.missingI18nElements.length > 0) {
    console.log('⚠️  Elements missing i18n:');
    report.missingI18nElements.forEach((element, index) => {
      console.log(`${index + 1}. ${element}`);
    });
  }
}

// Run all demonstrations
if (require.main === module) {
  demonstrateI18nAnalysis();
  demonstrateRealTemplateParsingIntegration();
  demonstrateI18nValidation();
  demonstrateI18nExtractionReport();
}

export {
  I18nAnalysisVisitor,
  I18nElementInfo,
  I18nValidationVisitor,
  I18nExtractionReportVisitor,
  demonstrateI18nAnalysis,
  demonstrateRealTemplateParsingIntegration,
  demonstrateI18nValidation,
  demonstrateI18nExtractionReport
};
