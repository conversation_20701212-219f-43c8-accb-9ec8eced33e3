/**
 * Minimal example: Find all TmplAstElement elements that have i18n property or attributes with i18n property
 * Uses parseTemplate from @angular/compiler to parse HTML template string
 */

// Mock parseTemplate function for demonstration
// In a real project, you would use: const { parseTemplate } = require('@angular/compiler');
function parseTemplate(template, templateUrl, options = {}) {
  // This is a simplified mock that creates template AST nodes
  // In reality, Angular's parseTemplate does complex HTML parsing and creates proper AST

  // Mock parsing - create elements based on HTML tags
  const mockNodes = [];

  // Simple regex to find HTML elements (this is very basic, real parsing is much more complex)
  const elementRegex = /<(\w+)([^>]*)>/g;
  let match;

  while ((match = elementRegex.exec(template)) !== null) {
    const tagName = match[1];
    const attributesStr = match[2];

    // Parse attributes
    const attributes = [];
    const attrRegex = /(\w+(?:-\w+)*)(?:="([^"]*)")?/g;
    let attrMatch;

    while ((attrMatch = attrRegex.exec(attributesStr)) !== null) {
      const attrName = attrMatch[1];
      const attrValue = attrMatch[2] || '';

      const attribute = {
        name: attrName,
        value: attrValue,
        visit: function(v) { return v.visitTextAttribute ? v.visitTextAttribute(this) : null; }
      };

      // Add i18n metadata if it's an i18n attribute
      if (attrName.startsWith('i18n-') || attrName === 'i18n') {
        attribute.i18n = { id: attrValue };
      }

      attributes.push(attribute);
    }

    // Create mock element
    const element = {
      name: tagName,
      attributes: attributes,
      children: [],
      visit: function(visitor) { return visitor.visitElement(this); }
    };

    // Add i18n property if element has i18n attribute
    const i18nAttr = attributes.find(attr => attr.name === 'i18n');
    if (i18nAttr) {
      element.i18n = { id: i18nAttr.value, description: 'Mock i18n' };
    }

    mockNodes.push(element);
  }

  return {
    nodes: mockNodes,
    errors: null
  };
}

// Simple recursive visitor
class TmplAstRecursiveVisitor {
  visitElement(element) {
    // Visit children recursively
    if (element.children) {
      element.children.forEach(child => {
        if (child && typeof child.visit === 'function') {
          child.visit(this);
        }
      });
    }
  }
}

// Visit all nodes in array
function tmplAstVisitAll(visitor, nodes) {
  for (const node of nodes) {
    if (node && typeof node.visit === 'function') {
      node.visit(visitor);
    }
  }
}

/**
 * Find all elements with i18n properties
 */
class I18nElementFinder extends TmplAstRecursiveVisitor {
  constructor() {
    super();
    this.foundElements = [];
  }

  visitElement(element) {
    // Check if element has i18n property
    const hasElementI18n = !!element.i18n;

    // Check if any attribute has i18n property or is an i18n attribute
    const hasAttributeI18n = element.attributes && element.attributes.some(attr => 
      attr.i18n || attr.name === 'i18n' || attr.name.startsWith('i18n-')
    );

    // If element has any i18n, add it to results
    if (hasElementI18n || hasAttributeI18n) {
      this.foundElements.push(element);
    }

    // Continue visiting children
    super.visitElement(element);
  }

  getFoundElements() {
    return this.foundElements;
  }
}

/**
 * Main function: Find all elements with i18n in template
 */
function findElementsWithI18n(templateNodes) {
  const finder = new I18nElementFinder();
  tmplAstVisitAll(finder, templateNodes);
  return finder.getFoundElements();
}

// Example usage with real HTML template string
function example() {
  // HTML template string to parse
  const htmlTemplate = `
    <div class="container">
      <h1 i18n="@@page.title">Welcome to our app</h1>
      <p i18n="@@page.description">This is an internationalized application.</p>
      <img src="/logo.png"
           alt="Company logo"
           i18n-alt="@@logo.alt">
      <button type="submit"
              title="Submit the form"
              i18n-title="@@form.submit.tooltip"
              i18n="@@form.submit.button">
        Submit
      </button>
      <div class="footer">
        <span>Copyright 2024</span>
      </div>
    </div>
  `;

  console.log('HTML Template:');
  console.log(htmlTemplate);
  console.log('\n=== Parsing with Angular parseTemplate ===\n');

  try {
    // Parse the HTML template using Angular's parseTemplate
    const parsed = parseTemplate(htmlTemplate, 'template.html', {
      preserveWhitespaces: false,
      enableI18nLegacyMessageIdFormat: false
    });

    if (parsed.errors && parsed.errors.length > 0) {
      console.log('Parse errors:', parsed.errors);
      return;
    }

    console.log(`Parsed ${parsed.nodes.length} root nodes`);

    // Find elements with i18n using our visitor
    const elementsWithI18n = findElementsWithI18n(parsed.nodes);

    console.log(`\nFound ${elementsWithI18n.length} elements with i18n properties:\n`);

    elementsWithI18n.forEach((element, index) => {
      console.log(`${index + 1}. <${element.name}>`);

      if (element.i18n) {
        console.log(`   ✓ Has element i18n property`);
      }

      if (element.attributes) {
        const i18nAttrs = element.attributes.filter(attr =>
          attr.i18n || attr.name === 'i18n' || attr.name.startsWith('i18n-')
        );
        if (i18nAttrs.length > 0) {
          console.log(`   ✓ Has i18n attributes:`);
          i18nAttrs.forEach(attr => {
            if (attr.name === 'i18n') {
              console.log(`     - i18n="${attr.value}"`);
            } else if (attr.name.startsWith('i18n-')) {
              console.log(`     - ${attr.name}="${attr.value}"`);
            } else if (attr.i18n) {
              console.log(`     - ${attr.name} (has i18n metadata)`);
            }
          });
        }
      }
      console.log('');
    });

  } catch (error) {
    console.error('Error parsing template:', error.message);
    console.log('\nNote: This example requires @angular/compiler to be installed.');
    console.log('Run: npm install @angular/compiler');
  }
}

// Run example
if (require.main === module) {
  example();
}

module.exports = { findElementsWithI18n, I18nElementFinder };
  example();

