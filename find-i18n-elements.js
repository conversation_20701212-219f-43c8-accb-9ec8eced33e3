/**
 * Minimal example: Find all TmplAstElement elements that have i18n property or attributes with i18n property
 */

// Simple recursive visitor
class TmplAstRecursiveVisitor {
  visitElement(element) {
    // Visit children recursively
    if (element.children) {
      element.children.forEach(child => {
        if (child && typeof child.visit === 'function') {
          child.visit(this);
        }
      });
    }
  }
}

// Visit all nodes in array
function tmplAstVisitAll(visitor, nodes) {
  for (const node of nodes) {
    if (node && typeof node.visit === 'function') {
      node.visit(visitor);
    }
  }
}

/**
 * Find all elements with i18n properties
 */
class I18nElementFinder extends TmplAstRecursiveVisitor {
  constructor() {
    super();
    this.foundElements = [];
  }

  visitElement(element) {
    // Check if element has i18n property
    const hasElementI18n = !!element.i18n;

    // Check if any attribute has i18n property or is an i18n attribute
    const hasAttributeI18n = element.attributes && element.attributes.some(attr => 
      attr.i18n || attr.name === 'i18n' || attr.name.startsWith('i18n-')
    );

    // If element has any i18n, add it to results
    if (hasElementI18n || hasAttributeI18n) {
      this.foundElements.push(element);
    }

    // Continue visiting children
    super.visitElement(element);
  }

  getFoundElements() {
    return this.foundElements;
  }
}

/**
 * Main function: Find all elements with i18n in template
 */
function findElementsWithI18n(templateNodes) {
  const finder = new I18nElementFinder();
  tmplAstVisitAll(finder, templateNodes);
  return finder.getFoundElements();
}

// Example usage
function example() {
  // Mock template nodes (in real usage, these come from parseTemplate)
  const templateNodes = [
    {
      name: 'div',
      attributes: [{ name: 'i18n', value: '@@welcome' }],
      children: [],
      i18n: { id: 'welcome' },
      visit: function(v) { return v.visitElement(this); }
    },
    {
      name: 'img', 
      attributes: [
        { name: 'alt', value: 'Logo', i18n: { id: 'logo-alt' } },
        { name: 'i18n-alt', value: '@@logo.alt' }
      ],
      children: [],
      visit: function(v) { return v.visitElement(this); }
    },
    {
      name: 'span',
      attributes: [{ name: 'class', value: 'text' }],
      children: [],
      visit: function(v) { return v.visitElement(this); }
    }
  ];

  // Find elements with i18n
  const elementsWithI18n = findElementsWithI18n(templateNodes);

  console.log('Elements with i18n properties:');
  elementsWithI18n.forEach(element => {
    console.log(`- <${element.name}>`);
    if (element.i18n) {
      console.log(`  Has element i18n property`);
    }
    if (element.attributes) {
      const i18nAttrs = element.attributes.filter(attr => 
        attr.i18n || attr.name === 'i18n' || attr.name.startsWith('i18n-')
      );
      if (i18nAttrs.length > 0) {
        console.log(`  Has i18n attributes: ${i18nAttrs.map(a => a.name).join(', ')}`);
      }
    }
  });
}

// Run example
if (require.main === module) {
  example();
}

module.exports = { findElementsWithI18n, I18nElementFinder };
