/**
 * Simple example: Find all TmplAstElement elements that have i18n properties
 */

import { 
  TmplAstNode, 
  TmplAstElement, 
  TmplAstRecursiveVisitor,
  tmplAstVisitAll
} from './tmpl-ast-visit-all';

// Simple interface for elements with i18n
interface ElementWithI18n {
  element: TmplAstElement;
  hasDirectI18n: boolean;
  hasI18nAttributes: boolean;
}

/**
 * Simple visitor to find all elements with i18n properties
 */
class SimpleI18nFinder extends TmplAstRecursiveVisitor {
  private elementsWithI18n: ElementWithI18n[] = [];

  /**
   * Get all elements that have any i18n properties
   */
  getElementsWithI18n(): ElementWithI18n[] {
    return this.elementsWithI18n;
  }

  /**
   * Visit each element and check for i18n properties
   */
  override visitElement(element: TmplAstElement): void {
    // Check if element has direct i18n property
    const hasDirectI18n = !!(element as any).i18n;

    // Check if any attribute has i18n property
    const hasI18nAttributes = element.attributes.some(attr => 
      (attr as any).i18n || attr.name === 'i18n' || attr.name.startsWith('i18n-')
    );

    // If element has any i18n, add it to results
    if (hasDirectI18n || hasI18nAttributes) {
      this.elementsWithI18n.push({
        element,
        hasDirectI18n,
        hasI18nAttributes
      });
    }

    // Continue visiting children
    super.visitElement(element);
  }
}

/**
 * Simple function to find all elements with i18n in a template
 */
function findElementsWithI18n(templateNodes: TmplAstNode[]): ElementWithI18n[] {
  const finder = new SimpleI18nFinder();
  tmplAstVisitAll(finder, templateNodes);
  return finder.getElementsWithI18n();
}

// Example usage:
export function demonstrateSimpleI18nFinder(): void {
  console.log('=== Simple I18n Element Finder ===\n');

  // Mock some template nodes for demonstration
  const mockNodes = createMockTemplateNodes();

  // Find all elements with i18n
  const elementsWithI18n = findElementsWithI18n(mockNodes);

  console.log(`Found ${elementsWithI18n.length} elements with i18n properties:\n`);

  elementsWithI18n.forEach((item, index) => {
    console.log(`${index + 1}. <${item.element.name}>`);
    if (item.hasDirectI18n) {
      console.log('   ✓ Has direct i18n property');
    }
    if (item.hasI18nAttributes) {
      console.log('   ✓ Has i18n attributes');
    }
    console.log('');
  });
}

// Mock data for demonstration
function createMockTemplateNodes(): TmplAstNode[] {
  // This would normally come from parseTemplate()
  // For demo purposes, we'll create mock nodes
  
  const mockElement1 = {
    name: 'div',
    attributes: [
      { name: 'i18n', value: '@@welcome' },
      { name: 'class', value: 'container' }
    ],
    inputs: [],
    outputs: [],
    children: [],
    references: [],
    sourceSpan: { toString: () => 'mock-span' },
    i18n: { id: 'welcome', description: 'Welcome message' },
    visit: function(visitor: any) { return visitor.visitElement(this); }
  } as any;

  const mockElement2 = {
    name: 'img',
    attributes: [
      { name: 'src', value: '/logo.png' },
      { name: 'alt', value: 'Logo', i18n: { id: 'logo-alt' } },
      { name: 'i18n-alt', value: '@@logo.alt' }
    ],
    inputs: [],
    outputs: [],
    children: [],
    references: [],
    sourceSpan: { toString: () => 'mock-span' },
    visit: function(visitor: any) { return visitor.visitElement(this); }
  } as any;

  const mockElement3 = {
    name: 'span',
    attributes: [
      { name: 'class', value: 'text' }
    ],
    inputs: [],
    outputs: [],
    children: [],
    references: [],
    sourceSpan: { toString: () => 'mock-span' },
    visit: function(visitor: any) { return visitor.visitElement(this); }
  } as any;

  return [mockElement1, mockElement2, mockElement3];
}

// Export the main functions
export { SimpleI18nFinder, findElementsWithI18n, ElementWithI18n };

// Run demo if this file is executed directly
if (require.main === module) {
  demonstrateSimpleI18nFinder();
}
