# Angular Template AST I18n Visitor Example

This example demonstrates how to use the TmplAstElement visitor pattern to identify internationalization (i18n) usage in Angular templates. It shows practical usage of the recursive visitor pattern for analyzing template AST nodes for internationalization purposes.

## Files

- `i18n-visitor-example.ts` - Complete TypeScript implementation with advanced features
- `i18n-visitor-demo.js` - Self-contained JavaScript demo that can be run immediately
- `i18n-visitor-README.md` - This documentation file

## Features

✅ **I18n Detection and Analysis**
- Identifies elements with direct `i18n` attributes
- Detects attribute-specific i18n (e.g., `i18n-title`, `i18n-alt`)
- Analyzes i18n metadata on elements and attributes
- Provides detailed reporting of i18n usage

✅ **Recursive Template Traversal**
- Uses `TmplAstRecursiveVisitor` for automatic child traversal
- Demonstrates manual recursive implementation
- Shows how to visit all children of `TmplAstElement`

✅ **Practical Examples**
- Sample HTML templates with various i18n scenarios
- Real-world use cases for i18n analysis
- Integration patterns with Angular's template parser

✅ **Multiple Visitor Implementations**
- `I18nAnalysisVisitor` - Basic i18n detection and collection
- `I18nValidationVisitor` - Validation and issue detection
- `I18nExtractionReportVisitor` - Comprehensive extraction reporting

## Quick Start

### Running the JavaScript Demo

```bash
cd /path/to/angular/project
node i18n-visitor-demo.js
```

### Sample HTML Template

The example analyzes templates like this:

```html
<div class="main-container">
  <div i18n="@@welcome" class="container">Welcome to our application!</div>
  <span title="Welcome message" i18n-title="@@welcome.title">Hover for tooltip</span>
  <img alt="Image description" i18n-alt="@@image.alt" src="/logo.png">
  <button i18n="@@button.submit" title="Submit form" i18n-title="@@button.submit.title">Click me</button>
  <div class="container">This is plain text</div>
</div>
```

## Usage Examples

### Basic I18n Analysis

```typescript
import { I18nAnalysisVisitor, tmplAstVisitAll } from './i18n-visitor-example';

// Create visitor
const i18nVisitor = new I18nAnalysisVisitor();

// Visit template nodes (from parseTemplate result)
tmplAstVisitAll(i18nVisitor, templateNodes);

// Get results
const i18nElements = i18nVisitor.getI18nElements();

console.log(`Found ${i18nElements.length} elements with i18n attributes`);
```

### I18n Validation

```typescript
import { I18nValidationVisitor } from './i18n-visitor-example';

const validationVisitor = new I18nValidationVisitor();
tmplAstVisitAll(validationVisitor, templateNodes);

const issues = validationVisitor.getValidationIssues();
issues.forEach(issue => {
  console.log(`${issue.severity}: ${issue.issue}`);
});
```

### Custom Visitor Implementation

```typescript
class CustomI18nVisitor extends TmplAstRecursiveVisitor {
  private results: any[] = [];

  override visitElement(element: TmplAstElement): void {
    // Custom analysis logic
    if (this.hasI18nContent(element)) {
      this.results.push({
        name: element.name,
        i18nInfo: this.extractI18nInfo(element)
      });
    }

    // Continue recursive traversal
    super.visitElement(element);
  }

  private hasI18nContent(element: TmplAstElement): boolean {
    // Check for i18n attributes
    return element.attributes.some(attr => 
      attr.name === 'i18n' || attr.name.startsWith('i18n-')
    );
  }
}
```

## Integration with Angular Compiler

In a real Angular application, you would integrate this with the Angular compiler:

```typescript
import { parseTemplate } from '@angular/compiler';
import { I18nAnalysisVisitor, tmplAstVisitAll } from './i18n-visitor-example';

// Parse HTML template
const parsed = parseTemplate(htmlTemplate, 'template.html', {
  preserveWhitespaces: false,
  enableI18nLegacyMessageIdFormat: false
});

// Analyze i18n usage
const i18nVisitor = new I18nAnalysisVisitor();
tmplAstVisitAll(i18nVisitor, parsed.nodes);

// Get results
const results = i18nVisitor.getI18nElements();
```

## I18n Patterns Detected

### Direct I18n Attributes
```html
<div i18n="@@welcome">Welcome</div>
<p i18n="meaning|description@@id">Text content</p>
```

### Attribute-Specific I18n
```html
<img alt="description" i18n-alt="@@image.alt">
<button title="tooltip" i18n-title="@@button.tooltip">Submit</button>
```

### Complex I18n Scenarios
```html
<div i18n="@@content" title="tooltip" i18n-title="@@tooltip">
  Content with both direct and attribute i18n
</div>
```

## Output Example

```
=== I18n Analysis Results ===

Found 4 elements with i18n attributes:

1. Element: <div>
   ✓ Direct i18n: "@@welcome"

2. Element: <span>
   ✓ I18n attributes:
     - i18n-title: "@@welcome.title" (for attribute: title)

3. Element: <img>
   ✓ I18n attributes:
     - i18n-alt: "@@image.alt" (for attribute: alt)

4. Element: <button>
   ✓ Direct i18n: "@@button.submit"
   ✓ I18n attributes:
     - i18n-title: "@@button.submit.title" (for attribute: title)

=== Summary ===
Elements with direct i18n: 2
Elements with i18n attributes: 3
Total i18n attributes found: 3
```

## Key Concepts

### Recursive Visitor Pattern
- **Automatic Traversal**: `TmplAstRecursiveVisitor` automatically visits all child nodes
- **Manual Control**: Override `visitElement()` to implement custom logic
- **Child Access**: Use `element.children` to access child nodes directly

### I18n Attribute Types
- **Direct i18n**: `i18n="@@id"` on elements with text content
- **Attribute i18n**: `i18n-attr="@@id"` for specific attributes
- **Metadata**: i18n information stored in AST node metadata

### Visitor Implementation
- **Extend Base Class**: Use `TmplAstRecursiveVisitor` for automatic recursion
- **Override Methods**: Implement `visitElement()` for custom analysis
- **Collect Results**: Store analysis results in visitor instance variables

## Use Cases

1. **I18n Coverage Analysis** - Identify which elements need internationalization
2. **Translation Extraction** - Generate lists of translatable content
3. **Validation** - Check for missing or malformed i18n attributes
4. **Reporting** - Create reports for translation teams
5. **Migration** - Assist in adding i18n to existing templates

This example demonstrates the power and flexibility of the visitor pattern for analyzing Angular template AST nodes, specifically for internationalization purposes.
