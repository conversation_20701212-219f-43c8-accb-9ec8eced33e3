/**
 * Simple example: Find all TmplAstElement elements that have i18n properties
 */

// Simple recursive visitor base class
class TmplAstRecursiveVisitor {
  visitElement(element) {
    // Visit all children recursively
    if (element.children) {
      element.children.forEach(child => {
        if (child && typeof child.visit === 'function') {
          child.visit(this);
        }
      });
    }
    
    // Visit attributes
    if (element.attributes) {
      element.attributes.forEach(attr => {
        if (attr && typeof attr.visit === 'function') {
          attr.visit(this);
        }
      });
    }
  }

  visitTextAttribute(attribute) {
    // Base implementation - do nothing
  }

  visitText(text) {
    // Base implementation - do nothing
  }
}

// Simple function to visit all nodes
function tmplAstVisitAll(visitor, nodes) {
  const results = [];
  
  for (const node of nodes) {
    if (node && typeof node.visit === 'function') {
      const result = node.visit(visitor);
      if (result !== null && result !== undefined) {
        results.push(result);
      }
    }
  }
  
  return results;
}

/**
 * Simple visitor to find all elements with i18n properties
 */
class SimpleI18nFinder extends TmplAstRecursiveVisitor {
  constructor() {
    super();
    this.elementsWithI18n = [];
  }

  /**
   * Get all elements that have any i18n properties
   */
  getElementsWithI18n() {
    return this.elementsWithI18n;
  }

  /**
   * Visit each element and check for i18n properties
   */
  visitElement(element) {
    // Check if element has direct i18n property
    const hasDirectI18n = !!element.i18n;

    // Check if any attribute has i18n property or is an i18n attribute
    const hasI18nAttributes = element.attributes && element.attributes.some(attr => 
      attr.i18n || attr.name === 'i18n' || attr.name.startsWith('i18n-')
    );

    // If element has any i18n, add it to results
    if (hasDirectI18n || hasI18nAttributes) {
      this.elementsWithI18n.push({
        element: element,
        hasDirectI18n: hasDirectI18n,
        hasI18nAttributes: hasI18nAttributes
      });
    }

    // Continue visiting children
    super.visitElement(element);
  }
}

/**
 * Simple function to find all elements with i18n in a template
 */
function findElementsWithI18n(templateNodes) {
  const finder = new SimpleI18nFinder();
  tmplAstVisitAll(finder, templateNodes);
  return finder.getElementsWithI18n();
}

// Example usage demonstration
function demonstrateSimpleI18nFinder() {
  console.log('=== Simple I18n Element Finder ===\n');

  // Create mock template nodes for demonstration
  const mockNodes = createMockTemplateNodes();

  console.log('Template structure:');
  console.log('- <div i18n="@@welcome"> (has direct i18n property)');
  console.log('- <img alt="Logo" i18n-alt="@@logo.alt"> (has i18n attribute)');
  console.log('- <span class="text"> (no i18n)');
  console.log('- <button i18n="@@submit" title="Click" i18n-title="@@tooltip"> (both)\n');

  // Find all elements with i18n
  const elementsWithI18n = findElementsWithI18n(mockNodes);

  console.log(`Found ${elementsWithI18n.length} elements with i18n properties:\n`);

  elementsWithI18n.forEach((item, index) => {
    console.log(`${index + 1}. <${item.element.name}>`);
    if (item.hasDirectI18n) {
      console.log('   ✓ Has direct i18n property');
    }
    if (item.hasI18nAttributes) {
      console.log('   ✓ Has i18n attributes');
      // Show which attributes are i18n-related
      const i18nAttrs = item.element.attributes.filter(attr => 
        attr.name === 'i18n' || attr.name.startsWith('i18n-') || attr.i18n
      );
      i18nAttrs.forEach(attr => {
        if (attr.name === 'i18n') {
          console.log(`     - i18n="${attr.value}"`);
        } else if (attr.name.startsWith('i18n-')) {
          console.log(`     - ${attr.name}="${attr.value}"`);
        } else if (attr.i18n) {
          console.log(`     - ${attr.name} (has i18n metadata)`);
        }
      });
    }
    console.log('');
  });

  console.log('=== Summary ===');
  console.log(`Total elements: ${mockNodes.length}`);
  console.log(`Elements with i18n: ${elementsWithI18n.length}`);
  console.log(`Elements with direct i18n: ${elementsWithI18n.filter(e => e.hasDirectI18n).length}`);
  console.log(`Elements with i18n attributes: ${elementsWithI18n.filter(e => e.hasI18nAttributes).length}`);
}

// Create mock template nodes for demonstration
function createMockTemplateNodes() {
  // Mock element 1: Has direct i18n property
  const element1 = {
    name: 'div',
    attributes: [
      { name: 'i18n', value: '@@welcome', visit: function(v) { return v.visitTextAttribute(this); } },
      { name: 'class', value: 'container', visit: function(v) { return v.visitTextAttribute(this); } }
    ],
    children: [],
    i18n: { id: 'welcome', description: 'Welcome message' },
    visit: function(visitor) { return visitor.visitElement(this); }
  };

  // Mock element 2: Has i18n attributes
  const element2 = {
    name: 'img',
    attributes: [
      { name: 'src', value: '/logo.png', visit: function(v) { return v.visitTextAttribute(this); } },
      { name: 'alt', value: 'Logo', i18n: { id: 'logo-alt' }, visit: function(v) { return v.visitTextAttribute(this); } },
      { name: 'i18n-alt', value: '@@logo.alt', visit: function(v) { return v.visitTextAttribute(this); } }
    ],
    children: [],
    visit: function(visitor) { return visitor.visitElement(this); }
  };

  // Mock element 3: No i18n (should not be found)
  const element3 = {
    name: 'span',
    attributes: [
      { name: 'class', value: 'text', visit: function(v) { return v.visitTextAttribute(this); } }
    ],
    children: [],
    visit: function(visitor) { return visitor.visitElement(this); }
  };

  // Mock element 4: Has both direct i18n and i18n attributes
  const element4 = {
    name: 'button',
    attributes: [
      { name: 'i18n', value: '@@submit', visit: function(v) { return v.visitTextAttribute(this); } },
      { name: 'title', value: 'Click me', visit: function(v) { return v.visitTextAttribute(this); } },
      { name: 'i18n-title', value: '@@tooltip', visit: function(v) { return v.visitTextAttribute(this); } }
    ],
    children: [],
    i18n: { id: 'submit', description: 'Submit button' },
    visit: function(visitor) { return visitor.visitElement(this); }
  };

  return [element1, element2, element3, element4];
}

// Run demonstration if this file is executed directly
if (require.main === module) {
  demonstrateSimpleI18nFinder();
}

// Export for use in other modules
module.exports = {
  SimpleI18nFinder,
  findElementsWithI18n,
  demonstrateSimpleI18nFinder,
  TmplAstRecursiveVisitor,
  tmplAstVisitAll
};
