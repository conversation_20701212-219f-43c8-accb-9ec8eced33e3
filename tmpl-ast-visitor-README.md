# Angular Template AST Visitor Implementation

This TypeScript implementation provides a `tmplAstVisitAll` function for iterating over child nodes in Angular's template AST (Abstract Syntax Tree), following the visitor pattern used in Angular's compiler codebase.

## Files

- `tmpl-ast-visit-all.ts` - Main implementation with interfaces and the `tmplAstVisitAll` function
- `tmpl-ast-visit-all-example.ts` - Examples and test cases demonstrating usage
- `tmpl-ast-visitor-README.md` - This documentation file

## Features

✅ **Complete Visitor Pattern Implementation**
- Supports both generic `visit()` method and specific typed visitor methods
- Compatible with Angular's existing AST structure and visitor patterns
- Handles all relevant template AST node types

✅ **Comprehensive Node Type Support**
- Elements, text nodes, attributes, directives
- Control flow blocks (if, for, switch, defer)
- Template references and variables
- Bound attributes and events
- ICU expressions

✅ **TypeScript Type Safety**
- Proper TypeScript typing for all interfaces
- Generic result type support
- Comprehensive error handling

✅ **Angular Compatibility**
- Based on Angular's `render3/r3_ast.ts` implementation
- Follows the same patterns as `tmplAstVisitAll` exported from `@angular/compiler`
- Compatible with existing Angular compiler infrastructure

## Core Function

### `tmplAstVisitAll<Result>(visitor: TmplAstVisitor<Result>, nodes: TmplAstNode[]): Result[]`

The main function that iterates through an array of template AST nodes and applies the appropriate visitor method based on the node type.

**Parameters:**
- `visitor` - Object implementing the `TmplAstVisitor<Result>` interface
- `nodes` - Array of template AST nodes to visit

**Returns:**
- Array of results from visiting each node

**Behavior:**
1. If the visitor has a generic `visit()` method that returns a truthy value, uses that result
2. Otherwise, calls the node's `visit()` method with the visitor to invoke the appropriate typed method
3. Collects and returns all non-null/undefined results
4. Provides error handling with descriptive error messages

## Visitor Interface

The `TmplAstVisitor<Result>` interface defines methods for visiting each type of template AST node:

### Core Template Nodes
- `visitElement(element)` - HTML elements (`<div>`, `<span>`, etc.)
- `visitTemplate(template)` - Template elements (`<ng-template>`)
- `visitContent(content)` - Content projection (`<ng-content>`)
- `visitText(text)` - Static text nodes
- `visitBoundText(text)` - Interpolated text (`{{ expression }}`)

### Attributes and Bindings
- `visitTextAttribute(attribute)` - Static attributes (`class="value"`)
- `visitBoundAttribute(attribute)` - Property bindings (`[property]="value"`)
- `visitBoundEvent(event)` - Event bindings (`(click)="handler()"`)

### Template References
- `visitVariable(variable)` - Template variables (`let item`)
- `visitReference(reference)` - Template references (`#ref`)

### Control Flow Blocks (Angular 17+)
- `visitIfBlock(block)` - `@if` blocks
- `visitForLoopBlock(block)` - `@for` blocks  
- `visitSwitchBlock(block)` - `@switch` blocks
- `visitDeferredBlock(block)` - `@defer` blocks

### Other Nodes
- `visitIcu(icu)` - Internationalization expressions
- `visitUnknownBlock(block)` - Custom or unknown blocks

## Usage Examples

### Example 1: Information Collector

```typescript
import { tmplAstVisitAll, TmplAstVisitor } from './tmpl-ast-visit-all';

class NodeInfoCollector implements TmplAstVisitor<string> {
  visitElement(element: TmplAstElement): string {
    return `Element: ${element.name}`;
  }

  visitText(text: TmplAstText): string {
    return `Text: "${text.value}"`;
  }

  // ... implement other required methods
}

const visitor = new NodeInfoCollector();
const results = tmplAstVisitAll(visitor, templateNodes);
console.log(results); // ['Element: div', 'Text: "Hello World"', ...]
```

### Example 2: Generic Visitor

```typescript
class GenericVisitor implements TmplAstVisitor<string> {
  visit(node: TmplAstNode): string {
    return `Visited: ${node.constructor.name}`;
  }

  // Other methods still required but won't be called
  // since visit() returns a truthy value
}
```

### Example 3: Recursive Visitor

```typescript
class NodeCounter extends TmplAstRecursiveVisitor {
  public count = 0;

  override visitElement(element: TmplAstElement): void {
    this.count++;
    super.visitElement(element); // Automatically visits children
  }

  override visitText(text: TmplAstText): void {
    this.count++;
    super.visitText(text);
  }
}

const counter = new NodeCounter();
tmplAstVisitAll(counter, templateNodes);
console.log(`Total nodes: ${counter.count}`);
```

## Error Handling

The function includes comprehensive error handling:

```typescript
try {
  const results = tmplAstVisitAll(visitor, nodes);
} catch (error) {
  console.error('Error visiting template AST:', error.message);
}
```

## Integration with Angular

This implementation is designed to be compatible with Angular's compiler package. To use with actual Angular template AST nodes:

```typescript
import { 
  TmplAstNode, 
  TmplAstVisitor, 
  tmplAstVisitAll as angularTmplAstVisitAll 
} from '@angular/compiler';

// Use Angular's built-in function
const results = angularTmplAstVisitAll(visitor, angularTemplateNodes);

// Or use this implementation with the same interface
import { tmplAstVisitAll } from './tmpl-ast-visit-all';
const results = tmplAstVisitAll(visitor, angularTemplateNodes);
```

## Running the Examples

To run the example code:

```bash
# Install dependencies
npm install @angular/compiler typescript

# Compile TypeScript
npx tsc tmpl-ast-visit-all-example.ts --target es2020 --module commonjs

# Run examples
node tmpl-ast-visit-all-example.js
```

## Testing

The implementation includes comprehensive test cases in the example file that demonstrate:

- Basic visitor functionality
- Generic vs. specific visitor methods
- Recursive visitor behavior
- Error handling scenarios
- Different node types and structures

## Architecture Notes

This implementation follows Angular's established patterns:

1. **Visitor Pattern** - Each node knows how to accept a visitor
2. **Generic Results** - Supports any result type through TypeScript generics
3. **Optional Generic Visit** - Visitors can implement a catch-all `visit()` method
4. **Recursive Traversal** - Base recursive visitor class for automatic child traversal
5. **Error Resilience** - Proper error handling and reporting

The code is structured to be maintainable, extensible, and compatible with Angular's evolving template AST structure.
