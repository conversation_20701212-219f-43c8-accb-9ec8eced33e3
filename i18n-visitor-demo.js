/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */

// Self-contained implementation for demonstration purposes
// In a real implementation, you would import these from '@angular/compiler'

/**
 * Visits all nodes in a template AST array using the provided visitor.
 */
function tmplAstVisitAll(visitor, nodes) {
  const result = [];

  try {
    for (const node of nodes) {
      if (!node || typeof node.visit !== 'function') {
        continue;
      }

      let visitResult;

      // Check if visitor has a generic visit method
      if (typeof visitor.visit === 'function') {
        visitResult = visitor.visit(node);
        if (visitResult !== null && visitResult !== undefined) {
          result.push(visitResult);
          continue;
        }
      }

      // Use the node's visit method to call the appropriate visitor method
      visitResult = node.visit(visitor);
      if (visitResult !== null && visitResult !== undefined) {
        result.push(visitResult);
      }
    }
  } catch (error) {
    throw new Error(`Error visiting template AST nodes: ${error.message}`);
  }

  return result;
}

/**
 * A base recursive visitor that can be extended to implement custom template AST traversal.
 */
class TmplAstRecursiveVisitor {
  visitElement(element) {
    tmplAstVisitAll(this, element.attributes);
    tmplAstVisitAll(this, element.inputs);
    tmplAstVisitAll(this, element.outputs);
    tmplAstVisitAll(this, element.children);
    tmplAstVisitAll(this, element.references);
  }

  visitTemplate(template) {
    tmplAstVisitAll(this, template.attributes);
    tmplAstVisitAll(this, template.inputs);
    tmplAstVisitAll(this, template.outputs);
    tmplAstVisitAll(this, template.templateAttrs);
    tmplAstVisitAll(this, template.children);
    tmplAstVisitAll(this, template.references);
    tmplAstVisitAll(this, template.variables);
  }

  visitContent(content) {
    tmplAstVisitAll(this, content.attributes);
  }

  visitVariable(variable) {}
  visitReference(reference) {}
  visitTextAttribute(attribute) {}
  visitBoundAttribute(attribute) {}
  visitBoundEvent(event) {}
  visitText(text) {}
  visitBoundText(text) {}
  visitIcu(icu) {}
}

// Mock ParseSourceSpan for testing
class MockParseSourceSpan {
  constructor(
    start = { offset: 0, line: 0, col: 0 },
    end = { offset: 0, line: 0, col: 0 },
    fullStart = { offset: 0, line: 0, col: 0 },
    details = null
  ) {
    this.start = start;
    this.end = end;
    this.fullStart = fullStart;
    this.details = details;
  }

  toString() {
    return 'mock-span';
  }
}

// Mock implementations for testing
class MockTmplAstTextAttribute {
  constructor(name, value, sourceSpan = new MockParseSourceSpan(), keySpan, valueSpan, i18n) {
    this.name = name;
    this.value = value;
    this.sourceSpan = sourceSpan;
    this.keySpan = keySpan;
    this.valueSpan = valueSpan;
    this.i18n = i18n;
  }

  visit(visitor) {
    return visitor.visitTextAttribute(this);
  }
}

class MockTmplAstElement {
  constructor(
    name,
    attributes = [],
    inputs = [],
    outputs = [],
    children = [],
    references = [],
    sourceSpan = new MockParseSourceSpan(),
    i18n
  ) {
    this.name = name;
    this.attributes = attributes;
    this.inputs = inputs;
    this.outputs = outputs;
    this.children = children;
    this.references = references;
    this.sourceSpan = sourceSpan;
    this.i18n = i18n;
  }

  visit(visitor) {
    return visitor.visitElement(this);
  }
}

class MockTmplAstText {
  constructor(value, sourceSpan = new MockParseSourceSpan()) {
    this.value = value;
    this.sourceSpan = sourceSpan;
  }

  visit(visitor) {
    return visitor.visitText(this);
  }
}

/**
 * Custom visitor that extends TmplAstRecursiveVisitor to identify i18n usage in templates.
 */
class I18nAnalysisVisitor extends TmplAstRecursiveVisitor {
  constructor() {
    super();
    this.i18nElements = [];
  }

  /**
   * Get all elements that have i18n-related attributes
   */
  getI18nElements() {
    return this.i18nElements;
  }

  /**
   * Visit an element and analyze it for i18n attributes
   */
  visitElement(element) {
    const i18nInfo = {
      elementName: element.name,
      hasDirectI18n: false,
      i18nAttributes: [],
      sourceLocation: element.sourceSpan.toString()
    };

    let hasI18nContent = false;

    // Check for direct i18n attribute on the element
    if (element.i18n) {
      i18nInfo.hasDirectI18n = true;
      i18nInfo.directI18nValue = this.extractI18nValue(element.i18n);
      hasI18nContent = true;
    }

    // Check all attributes for i18n-related content
    element.attributes.forEach(attr => {
      if (attr.name === 'i18n') {
        // Direct i18n attribute
        i18nInfo.hasDirectI18n = true;
        i18nInfo.directI18nValue = attr.value;
        hasI18nContent = true;
      } else if (attr.name.startsWith('i18n-')) {
        // i18n attribute for specific attribute (e.g., i18n-title, i18n-alt)
        const targetAttribute = attr.name.substring(5); // Remove 'i18n-' prefix
        i18nInfo.i18nAttributes.push({
          attributeName: targetAttribute,
          i18nKey: attr.name,
          i18nValue: attr.value
        });
        hasI18nContent = true;
      } else if (attr.i18n) {
        // Attribute has i18n metadata
        i18nInfo.i18nAttributes.push({
          attributeName: attr.name,
          i18nKey: 'i18n-' + attr.name,
          i18nValue: this.extractI18nValue(attr.i18n)
        });
        hasI18nContent = true;
      }
    });

    // Only add to results if the element has i18n content
    if (hasI18nContent) {
      this.i18nElements.push(i18nInfo);
    }

    // Continue recursive traversal
    super.visitElement(element);
  }

  /**
   * Extract i18n value from i18n metadata object
   */
  extractI18nValue(i18nMeta) {
    if (typeof i18nMeta === 'string') {
      return i18nMeta;
    }
    if (i18nMeta && typeof i18nMeta === 'object') {
      return i18nMeta.description || i18nMeta.meaning || i18nMeta.id || 'i18n-enabled';
    }
    return 'i18n-enabled';
  }
}

/**
 * Create sample template AST nodes that represent a parsed HTML template
 * with various i18n scenarios
 */
function createSampleTemplateNodes() {
  // Create attributes for different i18n scenarios
  const i18nAttr = new MockTmplAstTextAttribute('i18n', '@@welcome');
  const titleAttr = new MockTmplAstTextAttribute('title', 'Welcome message');
  const i18nTitleAttr = new MockTmplAstTextAttribute('i18n-title', '@@welcome.title');
  
  const altAttr = new MockTmplAstTextAttribute('alt', 'Image description');
  const i18nAltAttr = new MockTmplAstTextAttribute('i18n-alt', '@@image.alt');
  
  const classAttr = new MockTmplAstTextAttribute('class', 'container');

  // Create text nodes
  const welcomeText = new MockTmplAstText('Welcome to our application!');
  const buttonText = new MockTmplAstText('Click me');
  const plainText = new MockTmplAstText('This is plain text');

  // Create elements with different i18n configurations
  
  // 1. Element with direct i18n attribute
  const welcomeDiv = new MockTmplAstElement(
    'div',
    [i18nAttr, classAttr],
    [], [], 
    [welcomeText],
    []
  );

  // 2. Element with i18n on specific attribute (title)
  const tooltipSpan = new MockTmplAstElement(
    'span',
    [titleAttr, i18nTitleAttr],
    [], [],
    [new MockTmplAstText('Hover for tooltip')],
    []
  );

  // 3. Image with i18n alt attribute
  const imageElement = new MockTmplAstElement(
    'img',
    [altAttr, i18nAltAttr, new MockTmplAstTextAttribute('src', '/logo.png')],
    [], [], 
    [],
    []
  );

  // 4. Button with both direct i18n and i18n attribute
  const buttonElement = new MockTmplAstElement(
    'button',
    [
      new MockTmplAstTextAttribute('i18n', '@@button.submit'),
      new MockTmplAstTextAttribute('title', 'Submit form'),
      new MockTmplAstTextAttribute('i18n-title', '@@button.submit.title')
    ],
    [], [],
    [buttonText],
    []
  );

  // 5. Element without any i18n attributes (for comparison)
  const plainDiv = new MockTmplAstElement(
    'div',
    [classAttr],
    [], [],
    [plainText],
    []
  );

  // 6. Container element with i18n children
  const containerDiv = new MockTmplAstElement(
    'div',
    [new MockTmplAstTextAttribute('class', 'main-container')],
    [], [],
    [welcomeDiv, tooltipSpan, imageElement, buttonElement, plainDiv],
    []
  );

  return [containerDiv];
}

/**
 * Demonstrate the i18n analysis visitor
 */
function demonstrateI18nAnalysis() {
  console.log('=== I18n Template Analysis Example ===\n');

  // Create sample template nodes
  const templateNodes = createSampleTemplateNodes();

  console.log('Sample HTML Template (conceptual):');
  console.log(`
<div class="main-container">
  <div i18n="@@welcome" class="container">Welcome to our application!</div>
  <span title="Welcome message" i18n-title="@@welcome.title">Hover for tooltip</span>
  <img alt="Image description" i18n-alt="@@image.alt" src="/logo.png">
  <button i18n="@@button.submit" title="Submit form" i18n-title="@@button.submit.title">Click me</button>
  <div class="container">This is plain text</div>
</div>
  `);

  // Create and run the i18n analysis visitor
  const i18nVisitor = new I18nAnalysisVisitor();
  tmplAstVisitAll(i18nVisitor, templateNodes);

  // Get results
  const i18nElements = i18nVisitor.getI18nElements();

  console.log('\n=== I18n Analysis Results ===\n');
  console.log(`Found ${i18nElements.length} elements with i18n attributes:\n`);

  i18nElements.forEach((element, index) => {
    console.log(`${index + 1}. Element: <${element.elementName}>`);
    console.log(`   Source: ${element.sourceLocation}`);
    
    if (element.hasDirectI18n) {
      console.log(`   ✓ Direct i18n: "${element.directI18nValue}"`);
    }
    
    if (element.i18nAttributes.length > 0) {
      console.log(`   ✓ I18n attributes:`);
      element.i18nAttributes.forEach(attr => {
        console.log(`     - ${attr.i18nKey}: "${attr.i18nValue}" (for attribute: ${attr.attributeName})`);
      });
    }
    
    console.log('');
  });

  // Summary statistics
  const elementsWithDirectI18n = i18nElements.filter(el => el.hasDirectI18n).length;
  const elementsWithI18nAttrs = i18nElements.filter(el => el.i18nAttributes.length > 0).length;
  const totalI18nAttributes = i18nElements.reduce((sum, el) => sum + el.i18nAttributes.length, 0);

  console.log('=== Summary ===');
  console.log(`Elements with direct i18n: ${elementsWithDirectI18n}`);
  console.log(`Elements with i18n attributes: ${elementsWithI18nAttrs}`);
  console.log(`Total i18n attributes found: ${totalI18nAttributes}`);
}

/**
 * Example showing practical usage patterns for i18n analysis
 */
function demonstratePracticalUsage() {
  console.log('\n=== Practical Usage Patterns ===\n');

  console.log('1. Recursive Traversal:');
  console.log('   The visitor automatically traverses all child elements');
  console.log('   using the recursive visitor pattern.\n');

  console.log('2. I18n Detection:');
  console.log('   - Direct i18n attributes: i18n="@@id"');
  console.log('   - Attribute-specific i18n: i18n-title="@@id"');
  console.log('   - I18n metadata on elements and attributes\n');

  console.log('3. Integration with Angular:');
  console.log('   In a real Angular application, you would:');
  console.log('   - Import parseTemplate from @angular/compiler');
  console.log('   - Parse your template HTML into TmplAst nodes');
  console.log('   - Use these visitors to analyze i18n usage');
  console.log('   - Generate reports for translation teams\n');

  console.log('4. Custom Analysis:');
  console.log('   - Extend TmplAstRecursiveVisitor for custom logic');
  console.log('   - Override visitElement() to analyze specific patterns');
  console.log('   - Collect data during traversal');
  console.log('   - Generate reports or validation results\n');
}

// Run the demonstration
if (require.main === module) {
  demonstrateI18nAnalysis();
  demonstratePracticalUsage();
}

module.exports = {
  I18nAnalysisVisitor,
  TmplAstRecursiveVisitor,
  tmplAstVisitAll,
  demonstrateI18nAnalysis,
  demonstratePracticalUsage,
  MockTmplAstElement,
  MockTmplAstTextAttribute,
  MockTmplAstText
};
