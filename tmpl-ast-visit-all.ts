/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */

import { ParseSourceSpan } from '@angular/compiler';

/**
 * Base interface for all template AST nodes.
 * Each node has a source span and can accept a visitor.
 */
export interface TmplAstNode {
  sourceSpan: ParseSourceSpan;
  visit<Result>(visitor: TmplAstVisitor<Result>): Result;
}

/**
 * Visitor interface for template AST nodes.
 * Supports both generic visit method and specific typed visit methods.
 */
export interface TmplAstVisitor<Result = any> {
  /**
   * Generic visit method. If implemented, returning a truthy value will prevent
   * the call to the typed method and the returned value will be included in the result.
   */
  visit?(node: TmplAstNode): Result;

  // Core template node visitors
  visitElement(element: TmplAstElement): Result;
  visitTemplate(template: TmplAstTemplate): Result;
  visitContent(content: TmplAstContent): Result;
  visitVariable(variable: TmplAstVariable): Result;
  visitReference(reference: TmplAstReference): Result;
  visitTextAttribute(attribute: TmplAstTextAttribute): Result;
  visitBoundAttribute(attribute: TmplAstBoundAttribute): Result;
  visitBoundEvent(event: TmplAstBoundEvent): Result;
  visitText(text: TmplAstText): Result;
  visitBoundText(text: TmplAstBoundText): Result;
  visitIcu(icu: TmplAstIcu): Result;

  // Control flow block visitors
  visitDeferredBlock(deferred: TmplAstDeferredBlock): Result;
  visitDeferredBlockPlaceholder(block: TmplAstDeferredBlockPlaceholder): Result;
  visitDeferredBlockError(block: TmplAstDeferredBlockError): Result;
  visitDeferredBlockLoading(block: TmplAstDeferredBlockLoading): Result;
  visitDeferredTrigger(trigger: TmplAstDeferredTrigger): Result;
  visitSwitchBlock(block: TmplAstSwitchBlock): Result;
  visitSwitchBlockCase(block: TmplAstSwitchBlockCase): Result;
  visitForLoopBlock(block: TmplAstForLoopBlock): Result;
  visitForLoopBlockEmpty(block: TmplAstForLoopBlockEmpty): Result;
  visitIfBlock(block: TmplAstIfBlock): Result;
  visitIfBlockBranch(block: TmplAstIfBlockBranch): Result;
  visitUnknownBlock(block: TmplAstUnknownBlock): Result;
}

/**
 * Template AST node type definitions.
 * These interfaces represent the structure of Angular template AST nodes.
 */
export interface TmplAstElement extends TmplAstNode {
  name: string;
  attributes: TmplAstTextAttribute[];
  inputs: TmplAstBoundAttribute[];
  outputs: TmplAstBoundEvent[];
  children: TmplAstNode[];
  references: TmplAstReference[];
}

export interface TmplAstTemplate extends TmplAstNode {
  tagName: string;
  attributes: TmplAstTextAttribute[];
  inputs: TmplAstBoundAttribute[];
  outputs: TmplAstBoundEvent[];
  templateAttrs: (TmplAstTextAttribute | TmplAstBoundAttribute)[];
  children: TmplAstNode[];
  references: TmplAstReference[];
  variables: TmplAstVariable[];
}

export interface TmplAstContent extends TmplAstNode {
  selector: string;
  attributes: TmplAstTextAttribute[];
}

export interface TmplAstVariable extends TmplAstNode {
  name: string;
  value: string;
}

export interface TmplAstReference extends TmplAstNode {
  name: string;
  value: string;
}

export interface TmplAstTextAttribute extends TmplAstNode {
  name: string;
  value: string;
}

export interface TmplAstBoundAttribute extends TmplAstNode {
  name: string;
  type: any; // BindingType
  value: any; // AST
}

export interface TmplAstBoundEvent extends TmplAstNode {
  name: string;
  type: any; // ParsedEventType
  handler: any; // AST
}

export interface TmplAstText extends TmplAstNode {
  value: string;
}

export interface TmplAstBoundText extends TmplAstNode {
  value: any; // AST
}

export interface TmplAstIcu extends TmplAstNode {
  vars: Record<string, any>;
  placeholders: Record<string, any>;
}

// Control flow block interfaces
export interface TmplAstDeferredBlock extends TmplAstNode {
  children: TmplAstNode[];
  placeholder?: TmplAstDeferredBlockPlaceholder;
  loading?: TmplAstDeferredBlockLoading;
  error?: TmplAstDeferredBlockError;
  triggers: TmplAstDeferredTrigger[];
}

export interface TmplAstDeferredBlockPlaceholder extends TmplAstNode {
  children: TmplAstNode[];
}

export interface TmplAstDeferredBlockError extends TmplAstNode {
  children: TmplAstNode[];
}

export interface TmplAstDeferredBlockLoading extends TmplAstNode {
  children: TmplAstNode[];
}

export interface TmplAstDeferredTrigger extends TmplAstNode {
  // Trigger-specific properties would be defined here
}

export interface TmplAstSwitchBlock extends TmplAstNode {
  expression: any; // AST
  cases: TmplAstSwitchBlockCase[];
}

export interface TmplAstSwitchBlockCase extends TmplAstNode {
  expression: any; // AST | null
  children: TmplAstNode[];
}

export interface TmplAstForLoopBlock extends TmplAstNode {
  item: TmplAstVariable;
  expression: any; // AST
  trackBy: any; // AST
  contextVariables: TmplAstVariable[];
  children: TmplAstNode[];
  empty?: TmplAstForLoopBlockEmpty;
}

export interface TmplAstForLoopBlockEmpty extends TmplAstNode {
  children: TmplAstNode[];
}

export interface TmplAstIfBlock extends TmplAstNode {
  branches: TmplAstIfBlockBranch[];
}

export interface TmplAstIfBlockBranch extends TmplAstNode {
  expression: any; // AST | null
  children: TmplAstNode[];
  expressionAlias?: TmplAstVariable;
}

export interface TmplAstUnknownBlock extends TmplAstNode {
  name: string;
  children: TmplAstNode[];
}

/**
 * Visits all nodes in a template AST array using the provided visitor.
 * 
 * This function implements the visitor pattern used throughout Angular's compiler.
 * It iterates through each node in the array and applies the appropriate visitor method
 * based on the node type.
 * 
 * @param visitor The visitor object that implements TmplAstVisitor interface
 * @param nodes Array of template AST nodes to visit
 * @returns Array of results from visiting each node
 * 
 * @example
 * ```typescript
 * class MyVisitor implements TmplAstVisitor<string> {
 *   visitElement(element: TmplAstElement): string {
 *     return `Element: ${element.name}`;
 *   }
 *   
 *   visitText(text: TmplAstText): string {
 *     return `Text: ${text.value}`;
 *   }
 *   
 *   // ... implement other visit methods
 * }
 * 
 * const visitor = new MyVisitor();
 * const results = tmplAstVisitAll(visitor, templateNodes);
 * ```
 */
export function tmplAstVisitAll<Result>(
  visitor: TmplAstVisitor<Result>, 
  nodes: TmplAstNode[]
): Result[] {
  const result: Result[] = [];
  
  try {
    if (visitor.visit) {
      // If visitor has a generic visit method, use it first
      for (const node of nodes) {
        const visitResult = visitor.visit(node);
        if (visitResult) {
          // If generic visit returns a truthy value, use it and skip typed method
          result.push(visitResult);
        } else {
          // Otherwise, call the node's visit method with the visitor
          const nodeResult = node.visit(visitor);
          if (nodeResult !== undefined && nodeResult !== null) {
            result.push(nodeResult);
          }
        }
      }
    } else {
      // If no generic visit method, call each node's visit method directly
      for (const node of nodes) {
        const nodeResult = node.visit(visitor);
        if (nodeResult !== undefined && nodeResult !== null) {
          result.push(nodeResult);
        }
      }
    }
  } catch (error) {
    throw new Error(`Error visiting template AST nodes: ${error instanceof Error ? error.message : String(error)}`);
  }
  
  return result;
}

/**
 * A base recursive visitor that can be extended to implement custom template AST traversal.
 * This visitor automatically traverses child nodes for each node type.
 */
export abstract class TmplAstRecursiveVisitor implements TmplAstVisitor<void> {
  visitElement(element: TmplAstElement): void {
    tmplAstVisitAll(this, element.attributes);
    tmplAstVisitAll(this, element.inputs);
    tmplAstVisitAll(this, element.outputs);
    tmplAstVisitAll(this, element.children);
    tmplAstVisitAll(this, element.references);
  }

  visitTemplate(template: TmplAstTemplate): void {
    tmplAstVisitAll(this, template.attributes);
    tmplAstVisitAll(this, template.inputs);
    tmplAstVisitAll(this, template.outputs);
    tmplAstVisitAll(this, template.templateAttrs);
    tmplAstVisitAll(this, template.children);
    tmplAstVisitAll(this, template.references);
    tmplAstVisitAll(this, template.variables);
  }

  visitContent(content: TmplAstContent): void {
    tmplAstVisitAll(this, content.attributes);
  }

  visitVariable(variable: TmplAstVariable): void {}
  visitReference(reference: TmplAstReference): void {}
  visitTextAttribute(attribute: TmplAstTextAttribute): void {}
  visitBoundAttribute(attribute: TmplAstBoundAttribute): void {}
  visitBoundEvent(event: TmplAstBoundEvent): void {}
  visitText(text: TmplAstText): void {}
  visitBoundText(text: TmplAstBoundText): void {}
  visitIcu(icu: TmplAstIcu): void {}

  visitDeferredBlock(deferred: TmplAstDeferredBlock): void {
    tmplAstVisitAll(this, deferred.children);
    if (deferred.placeholder) {
      deferred.placeholder.visit(this);
    }
    if (deferred.loading) {
      deferred.loading.visit(this);
    }
    if (deferred.error) {
      deferred.error.visit(this);
    }
    tmplAstVisitAll(this, deferred.triggers);
  }

  visitDeferredBlockPlaceholder(block: TmplAstDeferredBlockPlaceholder): void {
    tmplAstVisitAll(this, block.children);
  }

  visitDeferredBlockError(block: TmplAstDeferredBlockError): void {
    tmplAstVisitAll(this, block.children);
  }

  visitDeferredBlockLoading(block: TmplAstDeferredBlockLoading): void {
    tmplAstVisitAll(this, block.children);
  }

  visitDeferredTrigger(trigger: TmplAstDeferredTrigger): void {}

  visitSwitchBlock(block: TmplAstSwitchBlock): void {
    tmplAstVisitAll(this, block.cases);
  }

  visitSwitchBlockCase(block: TmplAstSwitchBlockCase): void {
    tmplAstVisitAll(this, block.children);
  }

  visitForLoopBlock(block: TmplAstForLoopBlock): void {
    const blockItems = [block.item, ...block.contextVariables, ...block.children];
    if (block.empty) {
      blockItems.push(block.empty);
    }
    tmplAstVisitAll(this, blockItems);
  }

  visitForLoopBlockEmpty(block: TmplAstForLoopBlockEmpty): void {
    tmplAstVisitAll(this, block.children);
  }

  visitIfBlock(block: TmplAstIfBlock): void {
    tmplAstVisitAll(this, block.branches);
  }

  visitIfBlockBranch(block: TmplAstIfBlockBranch): void {
    const blockItems = [...block.children];
    if (block.expressionAlias) {
      blockItems.push(block.expressionAlias);
    }
    tmplAstVisitAll(this, blockItems);
  }

  visitUnknownBlock(block: TmplAstUnknownBlock): void {
    tmplAstVisitAll(this, block.children);
  }
}
