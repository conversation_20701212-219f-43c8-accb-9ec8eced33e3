/**
 * Example usage and tests for the tmplAstVisitAll function
 */

import { ParseSourceSpan } from '@angular/compiler';
import { 
  tmplAstVisitAll, 
  TmplAstVisitor, 
  TmplAstRecursiveVisitor,
  TmplAstNode, 
  TmplAstElement, 
  TmplAstText,
  TmplAstBoundAttribute,
  TmplAstTextAttribute,
  TmplAstTemplate,
  TmplAstVariable,
  TmplAstReference
} from './tmpl-ast-visit-all';

// Mock ParseSourceSpan for testing
class MockParseSourceSpan implements ParseSourceSpan {
  constructor(
    public start: any = { offset: 0, line: 0, col: 0 },
    public end: any = { offset: 0, line: 0, col: 0 }
  ) {}
  
  toString(): string {
    return 'mock-span';
  }
}

// Mock implementations of template AST nodes for testing
class MockTmplAstText implements TmplAstText {
  constructor(
    public value: string,
    public sourceSpan: ParseSourceSpan = new MockParseSourceSpan()
  ) {}

  visit<Result>(visitor: TmplAstVisitor<Result>): Result {
    return visitor.visitText(this);
  }
}

class MockTmplAstElement implements TmplAstElement {
  constructor(
    public name: string,
    public attributes: TmplAstTextAttribute[] = [],
    public inputs: TmplAstBoundAttribute[] = [],
    public outputs: any[] = [],
    public children: TmplAstNode[] = [],
    public references: TmplAstReference[] = [],
    public sourceSpan: ParseSourceSpan = new MockParseSourceSpan()
  ) {}

  visit<Result>(visitor: TmplAstVisitor<Result>): Result {
    return visitor.visitElement(this);
  }
}

class MockTmplAstTextAttribute implements TmplAstTextAttribute {
  constructor(
    public name: string,
    public value: string,
    public sourceSpan: ParseSourceSpan = new MockParseSourceSpan()
  ) {}

  visit<Result>(visitor: TmplAstVisitor<Result>): Result {
    return visitor.visitTextAttribute(this);
  }
}

// Example 1: Simple visitor that collects node information
class NodeInfoCollector implements TmplAstVisitor<string> {
  visitElement(element: TmplAstElement): string {
    return `Element: ${element.name}`;
  }

  visitText(text: TmplAstText): string {
    return `Text: "${text.value}"`;
  }

  visitTextAttribute(attribute: TmplAstTextAttribute): string {
    return `Attribute: ${attribute.name}="${attribute.value}"`;
  }

  visitTemplate(template: TmplAstTemplate): string {
    return `Template: ${template.tagName}`;
  }

  visitContent(content: any): string {
    return `Content: ${content.selector}`;
  }

  visitVariable(variable: TmplAstVariable): string {
    return `Variable: ${variable.name}`;
  }

  visitReference(reference: TmplAstReference): string {
    return `Reference: ${reference.name}`;
  }

  visitBoundAttribute(attribute: TmplAstBoundAttribute): string {
    return `BoundAttribute: ${attribute.name}`;
  }

  visitBoundEvent(event: any): string {
    return `BoundEvent: ${event.name}`;
  }

  visitBoundText(text: any): string {
    return `BoundText: ${text.value}`;
  }

  visitIcu(icu: any): string {
    return `ICU`;
  }

  // Control flow visitors
  visitDeferredBlock(deferred: any): string {
    return `DeferredBlock`;
  }

  visitDeferredBlockPlaceholder(block: any): string {
    return `DeferredBlockPlaceholder`;
  }

  visitDeferredBlockError(block: any): string {
    return `DeferredBlockError`;
  }

  visitDeferredBlockLoading(block: any): string {
    return `DeferredBlockLoading`;
  }

  visitDeferredTrigger(trigger: any): string {
    return `DeferredTrigger`;
  }

  visitSwitchBlock(block: any): string {
    return `SwitchBlock`;
  }

  visitSwitchBlockCase(block: any): string {
    return `SwitchBlockCase`;
  }

  visitForLoopBlock(block: any): string {
    return `ForLoopBlock`;
  }

  visitForLoopBlockEmpty(block: any): string {
    return `ForLoopBlockEmpty`;
  }

  visitIfBlock(block: any): string {
    return `IfBlock`;
  }

  visitIfBlockBranch(block: any): string {
    return `IfBlockBranch`;
  }

  visitUnknownBlock(block: any): string {
    return `UnknownBlock: ${block.name}`;
  }
}

// Example 2: Visitor with generic visit method
class GenericVisitor implements TmplAstVisitor<string> {
  visit(node: TmplAstNode): string {
    return `Generic visit: ${node.constructor.name}`;
  }

  // These methods won't be called because visit() returns a truthy value
  visitElement(element: TmplAstElement): string {
    return `Element: ${element.name}`;
  }

  visitText(text: TmplAstText): string {
    return `Text: "${text.value}"`;
  }

  // ... other required methods (simplified for brevity)
  visitTextAttribute(attribute: any): string { return ''; }
  visitTemplate(template: any): string { return ''; }
  visitContent(content: any): string { return ''; }
  visitVariable(variable: any): string { return ''; }
  visitReference(reference: any): string { return ''; }
  visitBoundAttribute(attribute: any): string { return ''; }
  visitBoundEvent(event: any): string { return ''; }
  visitBoundText(text: any): string { return ''; }
  visitIcu(icu: any): string { return ''; }
  visitDeferredBlock(deferred: any): string { return ''; }
  visitDeferredBlockPlaceholder(block: any): string { return ''; }
  visitDeferredBlockError(block: any): string { return ''; }
  visitDeferredBlockLoading(block: any): string { return ''; }
  visitDeferredTrigger(trigger: any): string { return ''; }
  visitSwitchBlock(block: any): string { return ''; }
  visitSwitchBlockCase(block: any): string { return ''; }
  visitForLoopBlock(block: any): string { return ''; }
  visitForLoopBlockEmpty(block: any): string { return ''; }
  visitIfBlock(block: any): string { return ''; }
  visitIfBlockBranch(block: any): string { return ''; }
  visitUnknownBlock(block: any): string { return ''; }
}

// Example 3: Recursive visitor that counts nodes
class NodeCounter extends TmplAstRecursiveVisitor {
  public count = 0;

  override visitElement(element: TmplAstElement): void {
    this.count++;
    console.log(`Visiting element: ${element.name}`);
    super.visitElement(element);
  }

  override visitText(text: TmplAstText): void {
    this.count++;
    console.log(`Visiting text: "${text.value}"`);
    super.visitText(text);
  }

  override visitTextAttribute(attribute: TmplAstTextAttribute): void {
    this.count++;
    console.log(`Visiting attribute: ${attribute.name}="${attribute.value}"`);
    super.visitTextAttribute(attribute);
  }
}

// Test function
function runExamples() {
  console.log('=== tmplAstVisitAll Examples ===\n');

  // Create sample template AST nodes
  const textNode = new MockTmplAstText('Hello World');
  const attributeNode = new MockTmplAstTextAttribute('class', 'my-class');
  const elementNode = new MockTmplAstElement('div', [attributeNode], [], [], [textNode]);
  
  const nodes: TmplAstNode[] = [elementNode, textNode];

  // Example 1: Using specific visitor methods
  console.log('Example 1: NodeInfoCollector');
  const infoCollector = new NodeInfoCollector();
  const infoResults = tmplAstVisitAll(infoCollector, nodes);
  console.log('Results:', infoResults);
  console.log();

  // Example 2: Using generic visit method
  console.log('Example 2: GenericVisitor');
  const genericVisitor = new GenericVisitor();
  const genericResults = tmplAstVisitAll(genericVisitor, nodes);
  console.log('Results:', genericResults);
  console.log();

  // Example 3: Using recursive visitor
  console.log('Example 3: NodeCounter (Recursive)');
  const nodeCounter = new NodeCounter();
  tmplAstVisitAll(nodeCounter, [elementNode]); // Only visit the element to see recursion
  console.log(`Total nodes visited: ${nodeCounter.count}`);
  console.log();

  // Example 4: Error handling
  console.log('Example 4: Error handling');
  try {
    const faultyVisitor = {
      visitElement: () => { throw new Error('Intentional error'); }
    } as any;
    tmplAstVisitAll(faultyVisitor, [elementNode]);
  } catch (error) {
    console.log('Caught error:', error instanceof Error ? error.message : String(error));
  }
}

// Run examples if this file is executed directly
if (require.main === module) {
  runExamples();
}

export { runExamples, NodeInfoCollector, GenericVisitor, NodeCounter };
